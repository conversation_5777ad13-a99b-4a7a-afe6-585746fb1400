// ================================================================
//                   第二章 新型生物气溶胶采样系统的优化设计与性能研究
// ================================================================
#import "../settings/heading.typ": *
#import "../settings/table.typ": styled-table, load-csv-simple, simple-table, load-csv-no-title
#import "../settings/figure.typ": *
#set math.equation(numbering: "(1)")




#new-chapter()
#chapter[第二章 新型生物气溶胶采样系统的优化设计与性能研究]



// ================================================================
//                   2.1 引言
// ================================================================
#section[引言]
引言测试@LiuChangHaiJiYuShuJuZengQiangHeTeZhengRongHeDeHuaFenTuXiangFenLei2025@WangYaQunJiYuShuJuZengGuangHeTeZhengRongHeDeHuaFenXiBaoTuXiangShiBie2020@ZhangXinYueJiYuTuXiangChuLiDeHuaFenShenDuTuXiangXiuZhengFangFaYanJiu2023@WangLiHuaFenGuoMinDeYanJiuXianZhuangYuYingDuiCuoShi2025


本章将介绍与研究相关的技术背景...



// ================================================================
//                   2.2 材料与方法
// ================================================================
#section[材料与方法]
// ================================================================
//                   2.2.1 实验材料与仪器
// ================================================================
#subsection[实验材料与仪器]

// 材料表格 - 使用CSV文件，无标题，自适应宽度
#load-csv-no-title("../assets/data/material02.csv")

#v(1em)

// 仪器表格 - 使用CSV文件，无标题，自适应宽度
#load-csv-no-title("../assets/data/instrument02.csv")

#v(1em)


详细介绍技术原理A...




// ================================================================
//                   2.2.2 新型高速湿壁气旋采样系统的设计
// ================================================================
#subsection[新型高速湿壁气旋采样系统的设计]
详细描述采样器的三维结构设计、关键尺寸参数、材料选择，以及核心驱动单元（22,200 rpm无刷直流电机）的规格与控制方式。





// ================================================================
//                   2.2.3 基于CFD-DPM的采样部署策略仿真研究
// ================================================================
#subsection[基于CFD-DPM的采样部署策略仿真研究]




为应对2019冠状病毒病（COVID-19）大流行期间的公共卫生挑战，需对检疫设施内的环境安全进行精确评估。本研究以2020年6月上海国际旅行卫生保健中心为入境旅客设立的临时鼻咽与血样采集环境为对象，旨在通过计算流体动力学（CFD）方法，阐明该特定空间内的气流组织形态与气溶胶输运机制。此项分析的目标在于为空气采样器的优化部署提供理论依据，并为评估现场消毒措施的有效性提供量化数据，从而保障医护人员及相关公众的健康安全。

本研究的计算域依据该临时采样设施的实际尺寸构建。该设施由两个联结的集装箱单元组成，总体内部尺寸为30 m（长）× 6 m（宽）× 2.7 m（高）。鉴于几何结构沿中心纵向平面（x=15 m）对称，为提升计算效率，仅对其中一半进行了建模。设施内部由一物理隔断（z=3 m）划分为检测人员工作区与待检旅客区，两者通过11个检测窗口（0.8 m × 0.8 m）连通，窗口下沿距地面高度为1 m。在旅客区域内，布设了用于收集空气样本的气溶胶采样器，其采样口（0.03 m × 0.03 m）设定在距地面1 m的高度。场地结构参照下图：

#side-by-side-figures(
  "../assets/figure/CFD01/CFD001.jpg",
  "../assets/figure/CFD01/CFD002.jpg",
  caption: "CFD-DPM仿真建模流程与参数设置",
  height: 8cm
  
)

设施内的通风系统由分体式空调与中央新风系统共同构成。检测人员区域内设有五台分体式空调，其送风口尺寸为0.8 m × 0.075 m，回风口尺寸为0.8 m × 0.15 m。同时，中央空调系统通过位于空调下方的圆形风口（直径0.3 m，中心高度2 m）向室内输送新风。在完成几何模型的构建与前处理后，采用非结构化多面体网格对计算域进行离散化，并对送/回风口、新风口、检测窗口、人员口鼻附近及壁面等高梯度区域施加了局部网格加密与边界层处理。


数值模拟在ANSYS Fluent 19.1平台下进行，求解过程采用稳态压力基求解器。室内通风场景中普遍存在射流、回流及大尺度涡旋等复杂流动现象，湍流模型选取了在预测自由剪切流与再循环流动方面表现稳健的Realizable k-ε模型。该模型的选用不仅因其在室内通风与污染物扩散模拟领域的广泛应用与验证，也为了确保与本项目既有技术流程的统一性与可复现性。模型开启了能量方程，并将空气密度设定为理想气体。此外，为追踪呼吸性飞沫的扩散路径，启用了组分输运模型，以CO₂作为示踪气体追踪呼吸性飞沫的扩散路径。求解过程中，压力与速度的耦合通过SIMPLE算法实现，动量、湍动能、能量以及组分输运方程的空间离散均采用二阶迎风格式。计算初始化采用混合初始化策略，残差收敛标准设定为 $1 times 10^(-5)$，同时对关键断面的物理量进行监测以确保达到稳定状态。

为了模拟呼吸性气溶胶的输运与沉降规律，本研究在欧拉连续相模型的基础上，引入了拉格朗日框架下的离散相模型（DPM）。病毒载体被抽象为惰性的球形颗粒，其密度$rho_p$设定为998 kg·m⁻³，并考虑了1 μm的代表性粒径。颗粒以$1 times 10^(-10)$ kg·s⁻¹的恒定质量流量从待检人员口鼻处释放。

颗粒在流场中主要受气动曳力与重力/浮力作用，曳力采用Schiller–Naumann经验公式计算。流场湍流对颗粒的弥散效应通过离散随机涡（DRW）模型描述，该模型通过让颗粒与随机生成的、具有有限生命周期的湍流涡相互作用来重现湍流扩散现象。


模型的边界条件根据物理场景进行了详尽设定。空调送风口与中央新风口均设为速度入口，风速为3 m/s，温度为24℃，其中空调送风方向与水平面呈45°角。空调回风口设为具有-1.5 m/s法向速度的入口以模拟抽风。待检人员口部设为速度为1 m/s的出口，其呼出气体温度为37℃，CO₂浓度为4% vol，并作为DPM颗粒的释放源；检测人员口部则设为-1 m/s的吸气边界。气溶胶采样器入口简化为质量流量出口，其值根据200 L/min的采样速率折算。待检人员区域的窗户定义为压力出口，表压为0 Pa。在热边界方面，所有人员体表均设为20 W·m⁻²的恒定热流密度，而舱体的顶棚、侧壁与地板则设为4 W·m⁻²的恒定热流密度。参数设定如下表格所示：



#load-csv-simple("../assets/data/CFD_settings001.csv", caption: "基于CFD-DPM的采样部署策略仿真边界条件设定")


#h(1.5em)
为确保方法的透明与可复现性，这里进一步交代建模假设与验证思路：连续相采用稳态RANS近似以刻画室内通风的统计平均特性；颗粒相采用一向耦合的稀相假设（颗粒体积分数极低，不反作用于流场），忽略蒸发与凝结等相变过程；近壁区通过边界层网格解析速度/温度梯度，并在关键截面监测体积流量守恒与能量平衡，以辅助判断收敛与物理合理性。此外，Realizable k-ε 的选用基于室内射流与再循环工况的大量验证经验，兼顾了本项目与既有流程的一致性。

为与现场评估目标保持一致，后处理中定义了两项与采样部署直接相关的评价指标。其一为采样器捕获效率，反映采样口所“捕获”的污染物通量占总呼出通量的比例；其二为窗口渗透率，反映污染物跨越检测窗口进入检测人员区域的比例。两者的数学定义如下：

$ 
E_("sam") = frac(M_("sam"), M_("mou")) 
$ <eq:esam>

$ 
E_("win") = frac(M_("win"), M_("mou")) 
$ <eq:ewin>

其中，$M_"sam"$ 为采样口截面的污染物质量流量，$M_"mou"$ 为通关人员口鼻处释放的污染物总质量流量，$M_"win"$ 为通过检测窗口截面的污染物质量流量。上述三者均通过后处理对相应截面进行积分获得。通过这两个指标，可以将“场景级的通风组织”与“采样器布点与能力”建立定量联系，用于指导采样部署与结果解读。

// ================================================================
//                   2.2.4 基于采样场景优化后的真实环境验证
// ================================================================
#subsection[基于采样场景优化后的真实环境验证]



针对6月19日-20日落地入境的5架航班临床样本采集过程进行环境样本富集与检测，所述5架航班包括MU5042（首尔-上海，259名旅客）、MU524（东京-上海，271名旅客）、AF198（巴黎-上海，102名旅客）、UL866（科伦坡-上海，222名旅客）以及CA850（伦敦-上海，377名旅客），其中由巴黎、科伦坡和伦敦起飞的三个航次为重点航班。

在乘客进行鼻咽拭子与血液样本采集过程中，采集采样舱中的空气样本和操作台面表面擦拭物样本。采样舱的空气样本采样点参照《GB/T 18204.3-2013 公共场所卫生检验方法第3部分:空气微生物》@GuoJiaJiKongJuGongGongChangSuoWeiShengJianYanFangFaDi3BuFenKongQiWeiShengWu2013，采集舱面积约为150 m²，选择2个采样点进行采集，分别设立在旅客通道入口与出口处，采样点距离地面高度为1.5 m，距离墙壁约1 m。选用大流量生物气溶胶采集装置，单次采集时间为5 min，采样流量为265 L/min，单个样本采集空气体积为1.325 m³，采集介质选用2.5ml 0.85% NaCl溶液，采集后将液体转移至样品管中用于检测。

操作台面表面擦拭物样本参照《GB15982-2012 医院消毒卫生标准》@GuoJiaWeiShengJianKangWeiYiYuanXiaoDuWeiShengBiaoZhun2012，在旅客进行标本采集及台面即时消杀操作前，用5cm × 5cm一次性规格板放在操作台面表面，用浸有0.85% NaCl采样液的拭子1支，在规格板内横竖各往返涂抹5次，并随之转动拭子。采样后，剪去拭子手接触部分，将其放入装有400 μL 采样液的试管中待检。

采集标本的检测选用环介导等温扩增技术，选用N基因和S基因作为靶标基因，进行非灭活免提取现场环境样本检测，取5 μL表面擦拭物样本或空气采集样本加入25μL检测体系中，并分别检测N基因响应和S基因响应，对阳性结果报告进行重复检测。每批次测试选用新型冠状病毒标准质粒作为阳性对照，选用0.85% NaCl溶液作为阴性对照，进行检测过程的质量控制。

具体采样时间安排如下：6月19日14：30-18：30，采集检测来自首尔、东京和巴黎旅客采集过程的环境样本，其中MU5042旅客采样过程中共采集6份空气样本（标记为KR-A-1至KR-A-6）和12份表面擦拭物样本（标记为KR-B-1至KR-B-12）；MU524旅客采样过程中共采集5份空气样本（标记为JP-A-1至JP-A-5）和8份表面擦拭物样本（标记为JP-B-1至JP-B-8）；AF198旅客采样过程中共采集4份空气样本（标记为FR-A-1至FR-A-4）和10份表面擦拭物样本（标记为FR-B-1至FR-B-10）。6月19日23：30-6月20日2：00，采集检测来自科伦坡旅客采集过程中的环境样本，UL866旅客采样过程中共采集8份空气样本（样品标记为LKA-A-1至LKA-A-8）和10份表面擦拭物样本（分别记为LKA-B-1至LKA-B-10）。6月20日7：00-11:30采集检测来自伦敦旅客采集过程中的环境样本，CA850旅客采样过程中共采集6份空气样本（标记为UK-A-1至UK-A-6）和18份表面擦拭物样本（标记为UK-B-1至UK-B-18）。共计采集27份空气气溶胶样本，58份表面擦拭物样本。




// ================================================================
//                   2.2.5 基于CFD-VOF气液界面动力学仿真研究
// ================================================================
#subsection[基于CFD-VOF液膜动力学仿真研究]


详述内壁超亲水涂层的制备工艺（如溶胶-凝胶法或化学气相沉积法）及表面接触角表征。阐述针对采样器内部建立的流体体积（VOF）多相流模型，其核心是通过设定高、低两种接触角工况，来精细模拟并对比有/无涂层时，气-液界面的动态演化行为。

// ================================================================
//                   2.2.6 内壁超亲水改性的研究
// ================================================================
#subsection[内壁超亲水改性的研究]


详述内壁超亲水涂层的制备工艺（如溶胶-凝胶法或化学气相沉积法）及表面接触角表征。阐述针对采样器内部建立的流体体积（VOF）多相流模型，其核心是通过设定高、低两种接触角工况，来精细模拟并对比有/无涂层时，气-液界面的动态演化行为。



// ================================================================
//                   2.2.6 荧光颗粒物的采样效率验证
// ================================================================
#subsection[基于CFD-VOF液膜动力学仿真研究]


详述内壁超亲水涂层的制备工艺（如溶胶-凝胶法或化学气相沉积法）及表面接触角表征。阐述针对采样器内部建立的流体体积（VOF）多相流模型，其核心是通过设定高、低两种接触角工况，来精细模拟并对比有/无涂层时，气-液界面的动态演化行为。

// ================================================================
//                   2.2.7 模拟仓真菌孢子暴露平台研究
// ================================================================
#subsection[模拟仓真菌孢子暴露平台研究]

描述在机场真实环境中进行病毒气溶胶采集的实验流程。详述在受控实验舱中，使用荧光微球验证ALTR提升的实验方案

// ================================================================
//                   2.2.8 真菌孢子的免疫学定量研究
// ================================================================
#subsection[真菌孢子的免疫学定量研究]

以及使用雾化的致敏真菌标志物，将优化后的采样器与两种商业化金标准设备（BioSampler, Sanki A-tec BM-300C）进行头对头（head-to-head）性能比较的实验设计。

// ================================================================
//                   第三节 结果与讨论
// ================================================================
#section[结果与讨论]


// ================================================================
//                   2.3.1 场景优化策略的CFD仿真
// ================================================================
#subsection[场景优化策略的CFD仿真]


呈现机场舱室的流场与颗粒物浓度分布云图，并据此论证采样器最佳布点的合理性。报告现场采样成功检出目标病毒的结果，并讨论这如何验证了“CFD指导部署”这一场景级优化策略的有效性。




// ================================================================
//                   2.3.2 基于CFD指导的部署策略的现场样本富集
// ================================================================
#subsection[基于CFD指导的部署策略的现场样本富集]


呈现机场舱室的流场与颗粒物浓度分布云图，并据此论证采样器最佳布点的合理性。报告现场采样成功检出目标病毒的结果，并讨论这如何验证了“CFD指导部署”这一场景级优化策略的有效性。


// ================================================================
//                   2.3.3 超亲水界面对液膜稳定性的提升
// ================================================================

#subsection[超亲水界面对液膜稳定性的提升]

超亲水表面对液膜稳定性和捕获效率的提升机制：本节为核心。首先，展示VOF仿真结果，通过对比动态图像，直观呈现无涂层内壁的液膜破裂、形成干区，以及超亲水内壁上连续、稳定液膜的形成。随后，呈现荧光微球的捕获实验结果，通过定量数据，证实超亲水改性显著提升了颗粒物的捕获效率，并讨论这一实验结果与VOF仿真预测的高度一致性，从而建立起从“表面工程”到“液膜稳定”再到“效率提升”的完整机理链条。

// ================================================================
//                   2.3.4 荧光颗粒物的
// ================================================================

#subsection[超亲水表面对捕获效率的提升机制]

超亲水表面对液膜稳定性和捕获效率的提升机制：本节为核心。首先，展示VOF仿真结果，通过对比动态图像，直观呈现无涂层内壁的液膜破裂、形成干区，以及超亲水内壁上连续、稳定液膜的形成。随后，呈现荧光微球的捕获实验结果，通过定量数据，证实超亲水改性显著提升了颗粒物的捕获效率，并讨论这一实验结果与VOF仿真预测的高度一致性，从而建立起从“表面工程”到“液膜稳定”再到“效率提升”的完整机理链条。



// ================================================================
//                   2.3.5 超亲水采样器的实验室验证
// ================================================================

#subsection[超亲水采样器的实验室采集效率]


以柱状图清晰展示本研究开发的采样器与两种商用设备在采集致敏真菌标志物浓度上的显著差异。通过统计学分析（如ANOVA检验），量化证明本系统在采集效率上的优越性，并讨论其优势的来源。




// ================================================================
//                   2.3.6 超亲水采样器的实验室采集效率
// ================================================================

#subsection[超亲水采样器的实验室采集效率]


以柱状图清晰展示本研究开发的采样器与两种商用设备在采集致敏真菌标志物浓度上的显著差异。通过统计学分析（如ANOVA检验），量化证明本系统在采集效率上的优越性，并讨论其优势的来源。


/*注释掉了
// 设置为横向页面
#page(flipped: true)[

#load-csv-simple("../assets/data/result.csv", caption: "实验结果")



]





// 你的横向内容（比如宽表格、图表等）

#styled-table(
  columns: 6,
  caption: "实验数据统计表",
  [列1], [列2], [列3], [列4], [列5], [列6],
  [数据1], [数据2], [数据3], [数据4], [数据5], [数据6],
  [更多数据1], [更多数据2], [更多数据3], [更多数据4], [更多数据5], [更多数据6],
)



*/
本研究中使用的主要工具包括...


// ================================================================
//                   第四节 本章小结
// ================================================================
#section[本章小结]






/*
#simple-table(
  columns: 6,
  data: "列1; 列2; 列3; 列4; 列5; 列6; 数据1; 数据2; 数据3; 数据4; 数据5; 数据6; 更多数据1; 更多数据2; 更多数据3; 更多数据4; 更多数据5; 更多数据6"
)

*/