# Typst 自适应表格宽度使用指南

## 概述

在 Typst 0.13.1 中，我们为表格系统添加了多种自适应宽度策略，让表格能够根据内容自动调整列宽，提供更好的视觉效果和可读性。

## 可用的宽度策略

### 1. 默认平均分配（原始方式）
```typst
#three-line-table(data, caption: "标题")
```
- 所有列平均分配可用宽度
- 适合列内容长度相近的情况

### 2. 自适应宽度
```typst
#three-line-table(data, caption: "标题", adaptive-width: true)
```
- 让 Typst 根据内容自动调整列宽
- 适合列内容长度差异较大的情况

### 3. 专门的自适应函数
```typst
#adaptive-width-table(data, caption: "标题")
```
- 完全自适应宽度
- 可选择手动指定列宽比例

### 4. 混合宽度策略
```typst
#mixed-width-table(data, caption: "标题", width-strategy: "策略名")
```

支持的策略：
- `"auto"`: 完全自适应
- `"equal"`: 平均分配
- `"balanced"`: 平衡策略（第一列稍宽）
- 自定义数组: 如 `(2fr, 1fr, 1fr)`

## 使用建议

### 何时使用自适应宽度
- 表格中有长短不一的文本内容
- 某些列需要更多空间显示完整信息
- 希望表格看起来更紧凑和专业

### 何时使用平均分配
- 所有列的内容长度相近
- 希望表格看起来整齐对称
- 表格宽度需要填满整个页面

### 何时使用混合策略
- 需要精确控制某些列的宽度
- 表格结构复杂，不同列有不同的重要性
- 需要在美观和功能之间找到平衡

## 实际应用示例

### 实验结果表格
```typst
// 使用平衡策略，方法名列稍宽，数值列紧凑
#mixed-width-table(
  data,
  caption: "实验结果对比",
  width-strategy: "balanced"
)
```

### 长描述表格
```typst
// 自定义列宽，描述列占更多空间
#mixed-width-table(
  data,
  caption: "功能描述",
  width-strategy: (1fr, 3fr, 1fr)
)
```

### 数值密集表格
```typst
// 完全自适应，让数值列紧凑显示
#mixed-width-table(
  data,
  caption: "统计数据",
  width-strategy: "auto"
)
```

## 技术细节

### 列宽单位说明
- `1fr`: 分数单位，表示占用剩余空间的比例
- `auto`: 根据内容自动调整
- `100pt`: 固定像素宽度
- `2cm`: 固定厘米宽度

### 平衡策略的默认设置
- 2列: `(1.5fr, 1fr)`
- 3列: `(1.5fr, 1fr, 1fr)`
- 4列: `(2fr, 1fr, 1fr, 1fr)`
- 5列及以上: `(2fr, 1fr, 1fr, ...)`

## 注意事项

1. **内容过长**: 自适应宽度可能导致某些列过宽，影响整体布局
2. **页面宽度**: 确保表格总宽度不超过页面边距
3. **一致性**: 在同一文档中保持表格样式的一致性
4. **可读性**: 优先考虑内容的可读性，而不是视觉对称性

## 迁移指南

如果你想将现有的表格改为自适应宽度：

1. **简单改动**: 在现有函数调用中添加 `adaptive-width: true`
2. **更多控制**: 使用 `mixed-width-table` 函数
3. **精确控制**: 使用自定义列宽数组

例如：
```typst
// 原来的代码
#three-line-table(data, caption: "标题")

// 改为自适应
#three-line-table(data, caption: "标题", adaptive-width: true)

// 或使用混合策略
#mixed-width-table(data, caption: "标题", width-strategy: "balanced")
```
