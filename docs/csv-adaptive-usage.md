# CSV自适应宽度表格使用指南

## 问题解决

你遇到的 "Unknown variable: load-csv-adaptive" 错误已经修复！现在可以正常使用了。

## 正确用法

### 1. 基本用法（完全自适应）

```typst
#load-csv-adaptive("../assets/data/阳性样本检测结果汇总.csv", 
                   caption: "阳性样本检测结果汇总", 
                   width-strategy: "auto")
```

### 2. 平衡宽度（推荐）

```typst
#load-csv-adaptive("../assets/data/阳性样本检测结果汇总.csv", 
                   caption: "阳性样本检测结果汇总", 
                   width-strategy: "balanced")
```

### 3. 自定义列宽

```typst
#load-csv-adaptive("../assets/data/阳性样本检测结果汇总.csv", 
                   caption: "阳性样本检测结果汇总", 
                   width-strategy: (2fr, 1fr, 1fr, 1fr, 1.5fr, 1fr))
```

## 路径注意事项

由于函数定义在 `settings/table.typ` 中，CSV文件路径需要相对于 `settings/` 目录：

- ✅ 正确：`"../assets/data/文件名.csv"`
- ❌ 错误：`"assets/data/文件名.csv"`

## 可用的宽度策略

1. **`"auto"`** - 完全根据内容自适应
2. **`"balanced"`** - 平衡策略，第一列稍宽
3. **自定义数组** - 如 `(2fr, 1fr, 1fr)`

## 针对你的数据推荐

根据你的CSV文件特点（样本编号 + 数值数据），推荐使用：

```typst
#load-csv-adaptive("../assets/data/阳性样本检测结果汇总.csv", 
                   caption: "阳性样本检测结果汇总", 
                   width-strategy: "balanced")
```

这样样本编号列会有足够空间，数值列保持紧凑。

## 快速替换现有代码

如果你之前使用：
```typst
#load-csv-simple("../assets/data/文件名.csv", caption: "标题")
```

现在可以改为：
```typst
#load-csv-adaptive("../assets/data/文件名.csv", caption: "标题", width-strategy: "balanced")
```

就能获得更好的显示效果！
