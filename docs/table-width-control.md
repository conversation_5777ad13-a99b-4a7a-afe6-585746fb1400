# 表格宽度控制使用指南

## 概述

现在所有表格函数都支持 `full-width` 参数，让你可以选择表格是占满页面宽度还是根据内容紧凑显示。

## 参数说明

### `full-width` 参数
- `true` (默认): 表格占满页面宽度
- `false`: 表格根据内容调整宽度，紧凑显示

### `adaptive-width` 参数
- `false` (默认): 所有列平均分配宽度
- `true`: 智能分配列宽（第一列稍宽）

## 使用方法

### 1. 手动表格

```typst
// 全宽度，平均分配（传统学术风格）
#manual-table(headers, rows, caption: "标题")

// 全宽度，智能分配
#manual-table(headers, rows, caption: "标题", 
              adaptive-width: true, full-width: true)

// 紧凑宽度（现代简洁风格）
#manual-table(headers, rows, caption: "标题", full-width: false)
```

### 2. CSV表格

```typst
// 全宽度显示CSV
#load-csv-simple("path/to/file.csv", caption: "标题", full-width: true)

// 紧凑宽度显示CSV
#load-csv-simple("path/to/file.csv", caption: "标题", full-width: false)
```

### 3. 自适应CSV表格

```typst
// 自适应表格默认是紧凑宽度的，如果需要全宽度：
#load-csv-adaptive("path/to/file.csv", caption: "标题", width-strategy: "balanced")
```

## 选择建议

### 使用全宽度 (`full-width: true`) 当：
- ✅ 制作正式文档（学术论文、报告）
- ✅ 希望表格看起来整齐对称
- ✅ 表格内容不多，有足够空间
- ✅ 遵循传统学术排版风格

### 使用紧凑宽度 (`full-width: false`) 当：
- ✅ 希望节省页面空间
- ✅ 表格内容简短紧凑
- ✅ 页面上有多个小表格
- ✅ 追求现代简洁的视觉效果

## 实际效果对比

### 全宽度表格
- 占满整个页面宽度
- 列宽均匀或按比例分配
- 适合正式文档

### 紧凑宽度表格
- 根据内容自动调整宽度
- 不会有多余的空白空间
- 适合简洁现代的排版

## 迁移指南

如果你想让现有的表格变为紧凑宽度：

```typst
// 原来的代码
#manual-table(headers, rows, caption: "标题")

// 改为紧凑宽度
#manual-table(headers, rows, caption: "标题", full-width: false)
```

如果你想让自适应表格变为全宽度，需要使用传统函数：

```typst
// 紧凑的自适应表格
#load-csv-adaptive("file.csv", caption: "标题")

// 全宽度的智能分配表格
#load-csv-simple("file.csv", caption: "标题", adaptive-width: true, full-width: true)
```

## 默认设置

- `full-width: true` - 保持与传统学术排版的兼容性
- `adaptive-width: false` - 保持列宽的一致性

这样你可以根据需要灵活选择表格的显示方式！
