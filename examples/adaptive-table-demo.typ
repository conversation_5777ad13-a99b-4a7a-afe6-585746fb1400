#import "../settings/table.typ": *

= 自适应表格宽度演示

== 1. 默认平均分配宽度（原始方式）

#three-line-table(
  (
    ("方法", "准确率", "召回率", "F1分数", "训练时间"),
    ("SVM", "0.85", "0.82", "0.83", "2.5小时"),
    ("Random Forest", "0.88", "0.86", "0.87", "1.8小时"),
    ("Neural Network", "0.92", "0.90", "0.91", "4.2小时")
  ),
  caption: "平均分配宽度示例"
)

== 2. 自适应宽度（根据内容调整）

#three-line-table(
  (
    ("方法", "准确率", "召回率", "F1分数", "训练时间"),
    ("SVM", "0.85", "0.82", "0.83", "2.5小时"),
    ("Random Forest", "0.88", "0.86", "0.87", "1.8小时"),
    ("Neural Network", "0.92", "0.90", "0.91", "4.2小时")
  ),
  caption: "自适应宽度示例",
  adaptive-width: true
)

== 3. 使用专门的自适应宽度函数

#adaptive-width-table(
  (
    ("算法名称", "精度", "召回", "F1", "时间"),
    ("支持向量机", "85.2%", "82.1%", "83.6%", "150分钟"),
    ("随机森林算法", "88.7%", "86.3%", "87.5%", "108分钟"),
    ("深度神经网络", "92.1%", "90.4%", "91.2%", "252分钟")
  ),
  caption: "完全自适应宽度"
)

== 4. 手动指定列宽比例

#adaptive-width-table(
  (
    ("方法", "准确率", "召回率", "F1分数", "训练时间"),
    ("SVM", "0.85", "0.82", "0.83", "2.5小时"),
    ("Random Forest", "0.88", "0.86", "0.87", "1.8小时"),
    ("Neural Network", "0.92", "0.90", "0.91", "4.2小时")
  ),
  caption: "手动指定列宽比例",
  column-widths: (2fr, 1fr, 1fr, 1fr, 1.5fr)  // 第一列和最后一列稍宽
)

== 5. 混合宽度策略 - 平衡模式

#mixed-width-table(
  (
    ("深度学习模型", "准确率", "召回率", "F1分数", "训练时间"),
    ("ResNet-50", "0.94", "0.92", "0.93", "6.8小时"),
    ("VGG-16", "0.89", "0.87", "0.88", "4.2小时"),
    ("MobileNet", "0.86", "0.84", "0.85", "1.5小时")
  ),
  caption: "平衡宽度策略",
  width-strategy: "balanced"
)

== 6. 混合宽度策略 - 完全自适应

#mixed-width-table(
  (
    ("模型", "精度", "速度", "内存占用"),
    ("BERT-Base", "92.5%", "慢", "高"),
    ("DistilBERT", "90.1%", "中等", "中等"),
    ("TinyBERT", "87.3%", "快", "低")
  ),
  caption: "完全自适应策略",
  width-strategy: "auto"
)

== 7. 自定义列宽数组

#mixed-width-table(
  (
    ("实验编号", "数据集", "模型架构", "批次大小", "学习率", "最终准确率"),
    ("Exp-001", "CIFAR-10", "CNN-3层", "32", "0.001", "89.2%"),
    ("Exp-002", "CIFAR-10", "ResNet-18", "64", "0.01", "94.1%"),
    ("Exp-003", "ImageNet", "ResNet-50", "128", "0.1", "76.8%")
  ),
  caption: "自定义列宽示例",
  width-strategy: (1fr, 1.5fr, 2fr, 1fr, 1fr, 1.2fr)
)

== 8. 处理长文本内容的表格

#mixed-width-table(
  (
    ("特征", "描述", "重要性"),
    ("文本长度", "输入文本的字符数量，影响模型处理时间", "高"),
    ("词汇丰富度", "文本中不重复词汇与总词汇的比例，反映语言多样性", "中"),
    ("语法复杂度", "句子结构的复杂程度，包括从句数量和嵌套层次", "中"),
    ("情感倾向", "文本表达的情感色彩，正面、负面或中性", "高")
  ),
  caption: "长文本内容处理",
  width-strategy: (1fr, 3fr, 1fr)  // 描述列占更多空间
)
