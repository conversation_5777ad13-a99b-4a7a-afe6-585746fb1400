#import "../settings/table.typ": *

= CSV文件自适应宽度表格演示

== 1. 传统方式（平均分配宽度）

使用原始的 `load-csv-simple` 函数：

```typst
#load-csv-simple("../assets/data/result.csv", caption: "传统平均宽度")
```

#load-csv-simple("../assets/data/result.csv", caption: "传统平均宽度")

== 2. 自适应宽度（推荐）

使用新的 `load-csv-adaptive` 函数，完全根据内容调整列宽：

```typst
#load-csv-adaptive("../assets/data/result.csv", caption: "自适应宽度")
```

#load-csv-adaptive("../assets/data/result.csv", caption: "自适应宽度")

== 3. 平衡宽度策略

第一列（通常是名称列）稍宽，其他列紧凑：

```typst
#load-csv-adaptive("../assets/data/result.csv", caption: "平衡宽度", width-strategy: "balanced")
```

#load-csv-adaptive("../assets/data/result.csv", caption: "平衡宽度", width-strategy: "balanced")

== 4. 自定义列宽比例

手动指定每列的宽度比例：

```typst
#load-csv-adaptive(
  "../assets/data/result.csv", 
  caption: "自定义列宽", 
  width-strategy: (2fr, 1fr, 1fr, 1fr, 1.5fr)
)
```

#load-csv-adaptive(
  "../assets/data/result.csv", 
  caption: "自定义列宽", 
  width-strategy: (2fr, 1fr, 1fr, 1fr, 1.5fr)
)

== 使用建议

=== 选择合适的策略

1. **完全自适应** (`width-strategy: "auto"`):
   - 适合：列内容长度差异很大
   - 优点：最紧凑，无浪费空间
   - 缺点：可能某些列过窄或过宽

2. **平衡策略** (`width-strategy: "balanced"`):
   - 适合：第一列是名称/标题，其他列是数据
   - 优点：视觉平衡，易读性好
   - 缺点：可能不是最紧凑的

3. **自定义比例**:
   - 适合：需要精确控制的专业表格
   - 优点：完全可控
   - 缺点：需要手动调整

=== 快速迁移指南

如果你现在使用：
```typst
#load-csv-simple("path/to/file.csv", caption: "标题")
```

只需改为：
```typst
#load-csv-adaptive("path/to/file.csv", caption: "标题")
```

就能获得自适应宽度！

=== 针对result.csv的便捷函数

我们还提供了专门针对result.csv的便捷函数：

```typst
// 自适应宽度
#result-table-adaptive()

// 平衡宽度
#result-table-balanced()
```

== 技术说明

自适应宽度的工作原理：
- Typst会分析每列的内容长度
- 自动计算最优的列宽分配
- 确保所有内容都能完整显示
- 同时保持表格的整体美观

这比手动设置固定宽度更智能，也比平均分配更高效！
