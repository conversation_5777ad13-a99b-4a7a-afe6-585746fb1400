#import "../settings/table.typ": *

= 表格宽度控制演示

== 1. 全宽度表格（默认，占满页面）

=== 平均分配列宽
```typst
#manual-table(
  ("方法", "准确率", "召回率", "F1分数"),
  (
    ("SVM", "0.85", "0.82", "0.83"),
    ("Random Forest", "0.88", "0.86", "0.87")
  ),
  caption: "全宽度 - 平均分配",
  full-width: true
)
```

#manual-table(
  ("方法", "准确率", "召回率", "F1分数"),
  (
    ("SVM", "0.85", "0.82", "0.83"),
    ("Random Forest", "0.88", "0.86", "0.87")
  ),
  caption: "全宽度 - 平均分配",
  full-width: true
)

=== 自适应分配（全宽度下的智能分配）
```typst
#manual-table(
  ("深度学习模型名称", "准确率", "召回率", "F1分数"),
  (
    ("ResNet-50", "0.94", "0.92", "0.93"),
    ("VGG-16", "0.89", "0.87", "0.88")
  ),
  caption: "全宽度 - 自适应分配",
  adaptive-width: true,
  full-width: true
)
```

#manual-table(
  ("深度学习模型名称", "准确率", "召回率", "F1分数"),
  (
    ("ResNet-50", "0.94", "0.92", "0.93"),
    ("VGG-16", "0.89", "0.87", "0.88")
  ),
  caption: "全宽度 - 自适应分配",
  adaptive-width: true,
  full-width: true
)

== 2. 紧凑宽度表格（根据内容调整）

=== 紧凑模式
```typst
#manual-table(
  ("方法", "准确率", "召回率", "F1分数"),
  (
    ("SVM", "0.85", "0.82", "0.83"),
    ("Random Forest", "0.88", "0.86", "0.87")
  ),
  caption: "紧凑宽度 - 根据内容调整",
  full-width: false
)
```

#manual-table(
  ("方法", "准确率", "召回率", "F1分数"),
  (
    ("SVM", "0.85", "0.82", "0.83"),
    ("Random Forest", "0.88", "0.86", "0.87")
  ),
  caption: "紧凑宽度 - 根据内容调整",
  full-width: false
)

=== 长内容的紧凑模式
```typst
#manual-table(
  ("深度学习模型名称", "准确率", "召回率", "F1分数"),
  (
    ("ResNet-50", "0.94", "0.92", "0.93"),
    ("VGG-16", "0.89", "0.87", "0.88")
  ),
  caption: "紧凑宽度 - 长内容",
  full-width: false
)
```

#manual-table(
  ("深度学习模型名称", "准确率", "召回率", "F1分数"),
  (
    ("ResNet-50", "0.94", "0.92", "0.93"),
    ("VGG-16", "0.89", "0.87", "0.88")
  ),
  caption: "紧凑宽度 - 长内容",
  full-width: false
)

== 3. CSV文件的宽度控制

=== 全宽度CSV表格
```typst
#load-csv-simple("../assets/data/result003.csv", 
                 caption: "CSV全宽度显示", 
                 full-width: true)
```

=== 紧凑宽度CSV表格
```typst
#load-csv-simple("../assets/data/result003.csv", 
                 caption: "CSV紧凑宽度显示", 
                 full-width: false)
```

== 使用建议

=== 何时使用全宽度 (`full-width: true`)
- **默认选择**：大多数情况下使用
- **正式文档**：学术论文、报告等
- **数据较少**：列数不多，内容不长
- **整齐美观**：希望表格看起来规整对称

=== 何时使用紧凑宽度 (`full-width: false`)
- **内容紧凑**：数据简短，不需要占满页面
- **多个表格**：页面上有多个小表格
- **节省空间**：希望在表格后继续添加其他内容
- **数据密集**：列数很多，全宽度会让每列过窄

=== 快速选择指南

```typst
// 传统学术风格（推荐）
#manual-table(headers, rows, caption: "标题", full-width: true)

// 紧凑现代风格
#manual-table(headers, rows, caption: "标题", full-width: false)

// 智能全宽度（第一列稍宽）
#manual-table(headers, rows, caption: "标题", 
              adaptive-width: true, full-width: true)
```

== 对比总结

| 模式 | 宽度 | 适用场景 | 视觉效果 |
|------|------|----------|----------|
| 全宽度 + 平均分配 | 占满页面 | 正式文档 | 整齐对称 |
| 全宽度 + 自适应 | 占满页面 | 列内容差异大 | 智能分配 |
| 紧凑宽度 | 根据内容 | 简洁现代 | 节省空间 |
