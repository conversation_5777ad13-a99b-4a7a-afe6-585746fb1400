#import "../settings/table.typ": *

= 你的CSV数据自适应宽度演示

== 1. 传统方式（所有列平均分配宽度）

```typst
#load-csv-simple("../assets/data/result003.csv", caption: "传统平均宽度")
```

#load-csv-simple("../assets/data/result003.csv", caption: "传统平均宽度")

== 2. 自适应宽度（推荐用法）

```typst
#load-csv-adaptive("../assets/data/result003.csv", caption: "自适应宽度")
```

#load-csv-adaptive("../assets/data/result003.csv", caption: "自适应宽度")

== 3. 平衡宽度（样本编号列稍宽）

```typst
#load-csv-adaptive("../assets/data/result003.csv", caption: "平衡宽度", width-strategy: "balanced")
```

#load-csv-adaptive("../assets/data/result003.csv", caption: "平衡宽度", width-strategy: "balanced")

== 4. 自定义宽度（样本编号列更宽）

针对你的数据特点，样本编号列需要更多空间：

```typst
#load-csv-adaptive(
  "../assets/data/result003.csv", 
  caption: "自定义宽度 - 样本编号列加宽", 
  width-strategy: (2.5fr, 1fr, 1fr, 1fr, 1fr, 1fr, 1fr)
)
```

#load-csv-adaptive(
  "../assets/data/result003.csv", 
  caption: "自定义宽度 - 样本编号列加宽", 
  width-strategy: (2.5fr, 1fr, 1fr, 1fr, 1fr, 1fr, 1fr)
)

== 5. 其他CSV文件示例

=== CFD设置数据
```typst
#load-csv-adaptive("../assets/data/CFD_settings001.csv", caption: "CFD设置参数")
```

#load-csv-adaptive("../assets/data/CFD_settings001.csv", caption: "CFD设置参数")

=== 中文表格数据
```typst
#load-csv-adaptive("../assets/data/各航班基本信息与感染者分布.csv", caption: "航班信息统计")
```

#load-csv-adaptive("../assets/data/各航班基本信息与感染者分布.csv", caption: "航班信息统计")

== 使用建议

根据你的数据特点，我推荐：

=== 对于实验数据表格（如result003.csv）
- **最佳选择**：使用平衡宽度策略
- **原因**：样本编号列内容较长，数值列内容较短
- **代码**：
```typst
#load-csv-adaptive("path/to/file.csv", caption: "标题", width-strategy: "balanced")
```

=== 对于中文表格
- **最佳选择**：完全自适应
- **原因**：中文字符宽度不一，自适应效果最好
- **代码**：
```typst
#load-csv-adaptive("path/to/file.csv", caption: "标题")
```

=== 快速替换现有代码

如果你现在的代码是：
```typst
#load-csv-simple("../assets/data/result003.csv", caption: "实验数据")
```

直接改为：
```typst
#load-csv-adaptive("../assets/data/result003.csv", caption: "实验数据", width-strategy: "balanced")
```

就能获得最佳的显示效果！

== 效果对比

- **传统方式**：所有列等宽，样本编号列可能显得拥挤，数值列有多余空间
- **自适应方式**：样本编号列获得足够空间，数值列紧凑显示，整体更美观
- **平衡策略**：在美观和实用性之间找到最佳平衡点
